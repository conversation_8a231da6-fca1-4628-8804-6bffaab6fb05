#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复评估结果格式问题
"""

import json
import os
import re
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def clean_evaluation_result(content: str) -> str:
    """
    清理评估结果内容，移除<think>标签等格式问题
    
    Args:
        content: 原始评估结果内容
        
    Returns:
        清理后的内容
    """
    if not content:
        return content
    
    # 移除<think>标签及其内容
    think_pattern = r'<think>.*?</think>'
    cleaned_content = re.sub(think_pattern, '', content, flags=re.DOTALL)
    
    # 清理多余的空行
    cleaned_content = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_content)
    cleaned_content = cleaned_content.strip()
    
    # 修复常见的格式错误
    # 修复编号错误：() 合规判断 -> (1) 合规判断
    cleaned_content = re.sub(r'\(\) 合规判断：', '(1) 合规判断：', cleaned_content)
    
    # 修复分析角度格式：分析：违反具体条款 -> 分析角度：违反具体条款
    cleaned_content = re.sub(r'\(2\) 分析：', '(2) 分析角度：', cleaned_content)
    
    return cleaned_content

def validate_format(content: str) -> tuple[bool, list]:
    """
    验证内容格式是否完整
    
    Args:
        content: 评估结果内容
        
    Returns:
        (是否格式完整, 缺失的元素列表)
    """
    required_patterns = [
        r'\*\*最终合规判断：\*\*',
        r'\*\*判断理由：\*\*',
        r'\*\*详细分析：\*\*',
        r'第一次审核：',
        r'第二次审核：',
        r'第三次审核：',
        r'第四次审核：',
        r'第五次审核：',
        r'\*\*投票结果统计：\*\*',
        r'\*\*最终优化建议：\*\*'
    ]
    
    missing_elements = []
    for pattern in required_patterns:
        if not re.search(pattern, content):
            missing_elements.append(pattern)
    
    return len(missing_elements) == 0, missing_elements

def fix_single_file(file_path: str) -> dict:
    """
    修复单个文件的格式问题
    
    Args:
        file_path: 文件路径
        
    Returns:
        修复结果信息
    """
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_content = data.get('合规评估结果', '')
        
        # 检查是否有<think>标签
        has_think_tag = '<think>' in original_content
        
        if not has_think_tag:
            # 验证格式完整性
            is_complete, missing = validate_format(original_content)
            if is_complete:
                return {
                    'file': os.path.basename(file_path),
                    'status': 'no_issues',
                    'message': '格式正确，无需修复'
                }
            else:
                return {
                    'file': os.path.basename(file_path),
                    'status': 'incomplete_format',
                    'message': f'格式不完整，缺少: {missing}'
                }
        
        # 清理内容
        cleaned_content = clean_evaluation_result(original_content)
        
        # 验证清理后的格式
        is_complete, missing = validate_format(cleaned_content)
        
        # 更新数据
        data['合规评估结果'] = cleaned_content
        data['格式修复时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data['原始格式问题'] = '包含<think>标签' if has_think_tag else '其他格式问题'
        
        # 创建备份
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 保存修复后的文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return {
            'file': os.path.basename(file_path),
            'status': 'fixed',
            'message': f'已修复<think>标签问题，格式完整性: {is_complete}',
            'backup_created': True
        }
        
    except Exception as e:
        return {
            'file': os.path.basename(file_path),
            'status': 'error',
            'message': f'修复失败: {str(e)}'
        }

def fix_all_files(results_folder: str) -> list:
    """
    修复所有文件的格式问题
    
    Args:
        results_folder: 结果文件夹路径
        
    Returns:
        修复结果列表
    """
    if not os.path.exists(results_folder):
        logging.error(f"文件夹不存在: {results_folder}")
        return []
    
    # 获取所有结果文件
    result_files = [f for f in os.listdir(results_folder) 
                   if f.endswith('_result.json') and f[0].isdigit()]
    result_files.sort()
    
    logging.info(f"找到 {len(result_files)} 个结果文件")
    
    fix_results = []
    
    for filename in result_files:
        file_path = os.path.join(results_folder, filename)
        result = fix_single_file(file_path)
        fix_results.append(result)
        
        if result['status'] == 'fixed':
            logging.info(f"已修复: {filename}")
        elif result['status'] == 'error':
            logging.error(f"修复失败: {filename} - {result['message']}")
    
    return fix_results

def generate_fix_report(fix_results: list) -> str:
    """
    生成修复报告
    
    Args:
        fix_results: 修复结果列表
        
    Returns:
        报告内容
    """
    report = []
    report.append("=" * 80)
    report.append("评估结果格式修复报告")
    report.append("=" * 80)
    report.append(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 统计信息
    total_files = len(fix_results)
    fixed_files = sum(1 for r in fix_results if r['status'] == 'fixed')
    no_issues_files = sum(1 for r in fix_results if r['status'] == 'no_issues')
    incomplete_files = sum(1 for r in fix_results if r['status'] == 'incomplete_format')
    error_files = sum(1 for r in fix_results if r['status'] == 'error')
    
    report.append(f"总文件数: {total_files}")
    report.append(f"已修复文件: {fixed_files}")
    report.append(f"无问题文件: {no_issues_files}")
    report.append(f"格式不完整文件: {incomplete_files}")
    report.append(f"修复失败文件: {error_files}")
    report.append("")
    
    # 详细结果
    if fixed_files > 0:
        report.append("已修复的文件:")
        report.append("-" * 40)
        for result in fix_results:
            if result['status'] == 'fixed':
                report.append(f"- {result['file']}: {result['message']}")
        report.append("")
    
    if incomplete_files > 0:
        report.append("格式不完整的文件:")
        report.append("-" * 40)
        for result in fix_results:
            if result['status'] == 'incomplete_format':
                report.append(f"- {result['file']}: {result['message']}")
        report.append("")
    
    if error_files > 0:
        report.append("修复失败的文件:")
        report.append("-" * 40)
        for result in fix_results:
            if result['status'] == 'error':
                report.append(f"- {result['file']}: {result['message']}")
        report.append("")
    
    return "\n".join(report)

def main():
    """主函数"""
    results_folder = "evaluation_results"
    
    logging.info("开始修复评估结果格式问题...")
    
    # 执行修复
    fix_results = fix_all_files(results_folder)
    
    if not fix_results:
        logging.error("没有找到可修复的文件")
        return
    
    # 生成报告
    report = generate_fix_report(fix_results)
    
    # 保存报告
    report_file = os.path.join(results_folder, "format_fix_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logging.info(f"修复报告已保存到: {report_file}")
    print("\n" + "=" * 50)
    print(report)

if __name__ == "__main__":
    main()
