#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析评估结果格式一致性问题
"""

import json
import os
import re
from typing import Dict, List, Any

def analyze_result_format(file_path: str) -> Dict[str, Any]:
    """
    分析单个结果文件的格式
    
    Args:
        file_path: 结果文件路径
        
    Returns:
        格式分析结果
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        result_content = data.get('合规评估结果', '')
        
        # 检查是否包含<think>标签
        has_think_tag = '<think>' in result_content
        
        # 检查是否严格遵循格式模板
        required_patterns = [
            r'\*\*最终合规判断：\*\* \[合规/不合规\]',
            r'\*\*判断理由：\*\*',
            r'\*\*详细分析：\*\*',
            r'第一次审核：',
            r'第二次审核：',
            r'第三次审核：',
            r'第四次审核：',
            r'第五次审核：',
            r'\*\*投票结果统计：\*\*',
            r'\*\*最终优化建议：\*\*'
        ]
        
        pattern_matches = {}
        for pattern in required_patterns:
            pattern_matches[pattern] = bool(re.search(pattern, result_content))
        
        # 检查格式完整性
        all_patterns_match = all(pattern_matches.values())
        
        # 检查是否有格式错误
        format_issues = []
        
        if has_think_tag:
            format_issues.append("包含<think>标签，显示了模型的思考过程")
        
        if not all_patterns_match:
            missing_patterns = [p for p, matched in pattern_matches.items() if not matched]
            format_issues.append(f"缺少必需的格式元素: {missing_patterns}")
        
        # 检查是否有格式不规范的地方
        if '() 合规判断：' in result_content:
            format_issues.append("存在编号错误，如'() 合规判断：'")
        
        if '分析：违反具体条款' in result_content and '分析角度：' not in result_content:
            format_issues.append("分析角度格式不一致")
        
        return {
            'file_name': os.path.basename(file_path),
            'has_think_tag': has_think_tag,
            'all_patterns_match': all_patterns_match,
            'pattern_matches': pattern_matches,
            'format_issues': format_issues,
            'content_length': len(result_content)
        }
        
    except Exception as e:
        return {
            'file_name': os.path.basename(file_path),
            'error': str(e)
        }

def analyze_all_results(results_folder: str) -> List[Dict[str, Any]]:
    """
    分析所有结果文件的格式
    
    Args:
        results_folder: 结果文件夹路径
        
    Returns:
        所有文件的格式分析结果
    """
    if not os.path.exists(results_folder):
        print(f"文件夹不存在: {results_folder}")
        return []
    
    # 获取所有结果文件
    result_files = [f for f in os.listdir(results_folder) 
                   if f.endswith('_result.json') and f[0].isdigit()]
    result_files.sort()
    
    print(f"找到 {len(result_files)} 个结果文件")
    
    all_analyses = []
    
    for filename in result_files:
        file_path = os.path.join(results_folder, filename)
        analysis = analyze_result_format(file_path)
        all_analyses.append(analysis)
    
    return all_analyses

def generate_format_report(analyses: List[Dict[str, Any]]) -> str:
    """
    生成格式分析报告
    
    Args:
        analyses: 格式分析结果列表
        
    Returns:
        格式化的报告字符串
    """
    report = []
    report.append("=" * 80)
    report.append("评估结果格式一致性分析报告")
    report.append("=" * 80)
    report.append("")
    
    # 统计信息
    total_files = len(analyses)
    files_with_think = sum(1 for a in analyses if a.get('has_think_tag', False))
    files_with_format_issues = sum(1 for a in analyses if a.get('format_issues', []))
    files_with_complete_format = sum(1 for a in analyses if a.get('all_patterns_match', False))
    
    report.append(f"总文件数: {total_files}")
    report.append(f"包含<think>标签的文件: {files_with_think}")
    report.append(f"格式完整的文件: {files_with_complete_format}")
    report.append(f"存在格式问题的文件: {files_with_format_issues}")
    report.append("")
    
    # 详细问题列表
    if files_with_think > 0:
        report.append("包含<think>标签的文件:")
        report.append("-" * 40)
        for analysis in analyses:
            if analysis.get('has_think_tag', False):
                report.append(f"- {analysis['file_name']}")
        report.append("")
    
    if files_with_format_issues > 0:
        report.append("存在格式问题的文件详情:")
        report.append("-" * 40)
        for analysis in analyses:
            if analysis.get('format_issues', []):
                report.append(f"文件: {analysis['file_name']}")
                for issue in analysis['format_issues']:
                    report.append(f"  - {issue}")
                report.append("")
    
    return "\n".join(report)

def main():
    """主函数"""
    results_folder = "evaluation_results"
    
    print("开始分析评估结果格式一致性...")
    analyses = analyze_all_results(results_folder)
    
    if not analyses:
        print("没有找到可分析的文件")
        return
    
    # 生成报告
    report = generate_format_report(analyses)
    
    # 保存报告
    report_file = os.path.join(results_folder, "format_analysis_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"格式分析报告已保存到: {report_file}")
    print("\n" + "=" * 50)
    print(report)

if __name__ == "__main__":
    main()
