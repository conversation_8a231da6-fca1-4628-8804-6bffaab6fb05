# 更新Python环境变量脚本
# 移除错误的Anaconda路径，添加正确的miniconda路径

Write-Host "正在更新Python环境变量..." -ForegroundColor Green

# 获取当前PATH
$currentPath = $env:PATH
Write-Host "当前PATH包含的Python相关路径:" -ForegroundColor Yellow

# 显示当前Python相关路径
$pythonPaths = $currentPath -split ';' | Where-Object { $_ -like "*python*" -or $_ -like "*anaconda*" -or $_ -like "*miniconda*" }
foreach ($path in $pythonPaths) {
    Write-Host "  - $path" -ForegroundColor Cyan
}

# 正确的miniconda路径
$correctPaths = @(
    "D:\ProgramData\miniconda3",
    "D:\ProgramData\miniconda3\Scripts",
    "D:\ProgramData\miniconda3\Library\bin"
)

Write-Host "`n建议的正确miniconda路径:" -ForegroundColor Yellow
foreach ($path in $correctPaths) {
    $exists = Test-Path $path
    $status = if ($exists) { "✓ 存在" } else { "✗ 不存在" }
    Write-Host "  - $path ($status)" -ForegroundColor $(if ($exists) { "Green" } else { "Red" })
}

Write-Host "`n注意: 要永久更新PATH环境变量，请:" -ForegroundColor Yellow
Write-Host "1. 打开系统属性 -> 高级 -> 环境变量" -ForegroundColor White
Write-Host "2. 在系统变量中找到PATH" -ForegroundColor White
Write-Host "3. 移除: D:\Anaconda3 和相关路径" -ForegroundColor Red
Write-Host "4. 添加: D:\ProgramData\miniconda3 和相关路径" -ForegroundColor Green
Write-Host "5. 重启PowerShell或重新登录" -ForegroundColor White

Write-Host "`n临时解决方案: 使用完整路径执行Python" -ForegroundColor Yellow
Write-Host "示例: & `"D:\ProgramData\miniconda3\python.exe`" script.py" -ForegroundColor Cyan
Write-Host "或使用提供的 run_py.ps1 脚本" -ForegroundColor Cyan
