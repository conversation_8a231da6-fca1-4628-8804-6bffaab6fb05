#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import os
import sys

def main():
    print("Python版本:", sys.version)
    print("当前工作目录:", os.getcwd())
    print("evaluation_results文件夹是否存在:", os.path.exists("evaluation_results"))
    
    if os.path.exists("evaluation_results"):
        files = os.listdir("evaluation_results")
        result_files = [f for f in files if f.endswith('_result.json')]
        print(f"找到 {len(result_files)} 个结果文件")
        if result_files:
            print("前5个文件:", result_files[:5])
    
    # 创建测试文件
    with open("test_output.txt", "w", encoding="utf-8") as f:
        f.write("测试成功！\n")
    
    print("测试文件已创建")

if __name__ == "__main__":
    main()
