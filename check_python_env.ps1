# 检查Python环境脚本

Write-Host "Python Environment Check" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# 检查当前PATH中的Python路径
Write-Host "`nCurrent Python-related paths in PATH:" -ForegroundColor Yellow
$env:PATH -split ';' | Where-Object { $_ -like "*python*" -or $_ -like "*anaconda*" -or $_ -like "*miniconda*" } | ForEach-Object {
    Write-Host "  - $_" -ForegroundColor Cyan
}

# 检查miniconda安装
Write-Host "`nChecking miniconda installation:" -ForegroundColor Yellow
$minicondaPath = "D:\ProgramData\miniconda3\python.exe"
if (Test-Path $minicondaPath) {
    Write-Host "  - Miniconda Python: FOUND" -ForegroundColor Green
    Write-Host "    Path: $minicondaPath" -ForegroundColor Cyan
} else {
    Write-Host "  - Miniconda Python: NOT FOUND" -ForegroundColor Red
}

# 测试Python版本
Write-Host "`nTesting Python execution:" -ForegroundColor Yellow
try {
    $version = & $minicondaPath --version 2>&1
    Write-Host "  - Version: $version" -ForegroundColor Green
} catch {
    Write-Host "  - Error executing Python: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nRecommendations:" -ForegroundColor Yellow
Write-Host "1. Use full path for Python execution" -ForegroundColor White
Write-Host "2. Use the provided run_py.ps1 script" -ForegroundColor White
Write-Host "3. Update system PATH to include correct miniconda paths" -ForegroundColor White
