================================================================================
评估结果格式修复报告
================================================================================
修复时间: 2025-07-19 10:18:15

总文件数: 101
已修复文件: 82
无问题文件: 18
格式不完整文件: 1
修复失败文件: 0

已修复的文件:
----------------------------------------
- 002_result.json: 已修复<think>标签问题，格式完整性: True
- 011_result.json: 已修复<think>标签问题，格式完整性: True
- 012_result.json: 已修复<think>标签问题，格式完整性: True
- 013_result.json: 已修复<think>标签问题，格式完整性: False
- 014_result.json: 已修复<think>标签问题，格式完整性: True
- 015_result.json: 已修复<think>标签问题，格式完整性: True
- 016_result.json: 已修复<think>标签问题，格式完整性: False
- 017_result.json: 已修复<think>标签问题，格式完整性: True
- 018_result.json: 已修复<think>标签问题，格式完整性: False
- 019_result.json: 已修复<think>标签问题，格式完整性: True
- 020_result.json: 已修复<think>标签问题，格式完整性: True
- 021_result.json: 已修复<think>标签问题，格式完整性: False
- 022_result.json: 已修复<think>标签问题，格式完整性: False
- 023_result.json: 已修复<think>标签问题，格式完整性: True
- 024_result.json: 已修复<think>标签问题，格式完整性: False
- 025_result.json: 已修复<think>标签问题，格式完整性: True
- 027_result.json: 已修复<think>标签问题，格式完整性: False
- 028_result.json: 已修复<think>标签问题，格式完整性: True
- 029_result.json: 已修复<think>标签问题，格式完整性: True
- 030_result.json: 已修复<think>标签问题，格式完整性: True
- 031_result.json: 已修复<think>标签问题，格式完整性: False
- 032_result.json: 已修复<think>标签问题，格式完整性: True
- 033_result.json: 已修复<think>标签问题，格式完整性: False
- 034_result.json: 已修复<think>标签问题，格式完整性: False
- 035_result.json: 已修复<think>标签问题，格式完整性: True
- 036_result.json: 已修复<think>标签问题，格式完整性: False
- 037_result.json: 已修复<think>标签问题，格式完整性: True
- 038_result.json: 已修复<think>标签问题，格式完整性: True
- 039_result.json: 已修复<think>标签问题，格式完整性: True
- 041_result.json: 已修复<think>标签问题，格式完整性: True
- 042_result.json: 已修复<think>标签问题，格式完整性: False
- 043_result.json: 已修复<think>标签问题，格式完整性: True
- 044_result.json: 已修复<think>标签问题，格式完整性: True
- 045_result.json: 已修复<think>标签问题，格式完整性: True
- 047_result.json: 已修复<think>标签问题，格式完整性: True
- 048_result.json: 已修复<think>标签问题，格式完整性: True
- 049_result.json: 已修复<think>标签问题，格式完整性: True
- 050_result.json: 已修复<think>标签问题，格式完整性: False
- 051_result.json: 已修复<think>标签问题，格式完整性: True
- 052_result.json: 已修复<think>标签问题，格式完整性: True
- 053_result.json: 已修复<think>标签问题，格式完整性: True
- 054_result.json: 已修复<think>标签问题，格式完整性: False
- 056_result.json: 已修复<think>标签问题，格式完整性: False
- 057_result.json: 已修复<think>标签问题，格式完整性: True
- 059_result.json: 已修复<think>标签问题，格式完整性: True
- 060_result.json: 已修复<think>标签问题，格式完整性: False
- 061_result.json: 已修复<think>标签问题，格式完整性: True
- 062_result.json: 已修复<think>标签问题，格式完整性: False
- 063_result.json: 已修复<think>标签问题，格式完整性: True
- 064_result.json: 已修复<think>标签问题，格式完整性: True
- 065_result.json: 已修复<think>标签问题，格式完整性: True
- 066_result.json: 已修复<think>标签问题，格式完整性: False
- 067_result.json: 已修复<think>标签问题，格式完整性: True
- 068_result.json: 已修复<think>标签问题，格式完整性: True
- 069_result.json: 已修复<think>标签问题，格式完整性: True
- 070_result.json: 已修复<think>标签问题，格式完整性: True
- 071_result.json: 已修复<think>标签问题，格式完整性: False
- 072_result.json: 已修复<think>标签问题，格式完整性: True
- 073_result.json: 已修复<think>标签问题，格式完整性: False
- 074_result.json: 已修复<think>标签问题，格式完整性: True
- 075_result.json: 已修复<think>标签问题，格式完整性: True
- 076_result.json: 已修复<think>标签问题，格式完整性: True
- 077_result.json: 已修复<think>标签问题，格式完整性: True
- 078_result.json: 已修复<think>标签问题，格式完整性: False
- 079_result.json: 已修复<think>标签问题，格式完整性: True
- 080_result.json: 已修复<think>标签问题，格式完整性: False
- 081_result.json: 已修复<think>标签问题，格式完整性: False
- 082_result.json: 已修复<think>标签问题，格式完整性: True
- 083_result.json: 已修复<think>标签问题，格式完整性: True
- 085_result.json: 已修复<think>标签问题，格式完整性: True
- 086_result.json: 已修复<think>标签问题，格式完整性: True
- 088_result.json: 已修复<think>标签问题，格式完整性: True
- 089_result.json: 已修复<think>标签问题，格式完整性: False
- 090_result.json: 已修复<think>标签问题，格式完整性: False
- 091_result.json: 已修复<think>标签问题，格式完整性: True
- 092_result.json: 已修复<think>标签问题，格式完整性: True
- 095_result.json: 已修复<think>标签问题，格式完整性: True
- 096_result.json: 已修复<think>标签问题，格式完整性: True
- 097_result.json: 已修复<think>标签问题，格式完整性: True
- 099_result.json: 已修复<think>标签问题，格式完整性: False
- 100_result.json: 已修复<think>标签问题，格式完整性: True
- 101_result.json: 已修复<think>标签问题，格式完整性: False

格式不完整的文件:
----------------------------------------
- 046_result.json: 格式不完整，缺少: ['\\*\\*最终合规判断：\\*\\*', '\\*\\*判断理由：\\*\\*', '\\*\\*详细分析：\\*\\*', '第一次审核：', '第二次审核：', '第三次审核：', '第四次审核：', '第五次审核：', '\\*\\*投票结果统计：\\*\\*', '\\*\\*最终优化建议：\\*\\*']
