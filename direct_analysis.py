#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接执行格式分析，不依赖外部执行
"""

import json
import os
import re

# 直接在这里执行分析
def analyze_format_issues():
    results_folder = "evaluation_results"
    
    if not os.path.exists(results_folder):
        return "错误：evaluation_results文件夹不存在"
    
    # 获取所有结果文件
    result_files = [f for f in os.listdir(results_folder) 
                   if f.endswith('_result.json') and f[0].isdigit()]
    result_files.sort()
    
    if not result_files:
        return "错误：没有找到结果文件"
    
    # 分析每个文件
    issues_found = []
    files_with_think = []
    files_with_format_problems = []
    
    for filename in result_files[:10]:  # 先分析前10个文件
        file_path = os.path.join(results_folder, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            result_content = data.get('合规评估结果', '')
            
            # 检查是否包含<think>标签
            if '<think>' in result_content:
                files_with_think.append(filename)
            
            # 检查必需的格式元素
            required_patterns = [
                r'\*\*最终合规判断：\*\*',
                r'\*\*判断理由：\*\*',
                r'\*\*详细分析：\*\*',
                r'第一次审核：',
                r'第二次审核：',
                r'第三次审核：',
                r'第四次审核：',
                r'第五次审核：',
                r'\*\*投票结果统计：\*\*',
                r'\*\*最终优化建议：\*\*'
            ]
            
            missing_patterns = []
            for pattern in required_patterns:
                if not re.search(pattern, result_content):
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                files_with_format_problems.append({
                    'file': filename,
                    'missing': missing_patterns
                })
            
            # 检查其他格式问题
            format_issues = []
            if '() 合规判断：' in result_content:
                format_issues.append("存在编号错误")
            
            if format_issues:
                issues_found.append({
                    'file': filename,
                    'issues': format_issues
                })
                
        except Exception as e:
            issues_found.append({
                'file': filename,
                'issues': [f"文件读取错误: {str(e)}"]
            })
    
    # 生成报告
    report = []
    report.append("=" * 60)
    report.append("评估结果格式分析报告")
    report.append("=" * 60)
    report.append(f"分析文件数: {len(result_files[:10])}")
    report.append(f"包含<think>标签的文件数: {len(files_with_think)}")
    report.append(f"格式不完整的文件数: {len(files_with_format_problems)}")
    report.append(f"其他格式问题的文件数: {len(issues_found)}")
    report.append("")
    
    if files_with_think:
        report.append("包含<think>标签的文件:")
        for f in files_with_think:
            report.append(f"  - {f}")
        report.append("")
    
    if files_with_format_problems:
        report.append("格式不完整的文件:")
        for item in files_with_format_problems:
            report.append(f"  - {item['file']}: 缺少 {len(item['missing'])} 个必需元素")
        report.append("")
    
    if issues_found:
        report.append("其他格式问题:")
        for item in issues_found:
            report.append(f"  - {item['file']}: {', '.join(item['issues'])}")
    
    return "\n".join(report)

# 执行分析并保存结果
try:
    result = analyze_format_issues()
    
    # 保存到文件
    with open("evaluation_results/format_analysis_direct.txt", "w", encoding="utf-8") as f:
        f.write(result)
    
    print("分析完成，结果已保存到 evaluation_results/format_analysis_direct.txt")
    print("\n" + result)
    
except Exception as e:
    print(f"分析过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
