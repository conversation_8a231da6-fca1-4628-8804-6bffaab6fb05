# PowerShell脚本：使用正确的miniconda Python路径执行Python脚本
# 用法: .\run_python.ps1 script_name.py [arguments]

param(
    [Parameter(Mandatory = $true)]
    [string]$ScriptName,
    
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$Arguments
)

# 设置正确的Python路径
$PythonPath = "D:\ProgramData\miniconda3\python.exe"

# 检查Python是否存在
if (-not (Test-Path $PythonPath)) {
    Write-Error "Python not found at: $PythonPath"
    exit 1
}

# 检查脚本文件是否存在
if (-not (Test-Path $ScriptName)) {
    Write-Error "Script not found: $ScriptName"
    exit 1
}

# 构建完整的命令
Write-Host "Executing Python script: $ScriptName" -ForegroundColor Green

# 执行命令
if ($Arguments) {
    & $PythonPath $ScriptName $Arguments
}
else {
    & $PythonPath $ScriptName
}
